<template>
  <div class="vue-office-preview">
    <!-- 加载状态 -->
    <div v-if="loading" class="preview-loading">
      <el-icon class="is-loading" :size="48">
        <Loading />
      </el-icon>
      <p>正在加载预览...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="preview-error">
      <el-icon :size="48" color="#F56C6C">
        <Warning />
      </el-icon>
      <p>{{ error }}</p>
    </div>
    
    <!-- Vue Office 预览组件 -->
    <div v-else class="office-preview-container">
      <!-- Word 文档预览 -->
      <vue-office-docx
        v-if="previewType === 'docx' && documentSrc && isReady"
        :key="`docx-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- Excel 文档预览 -->
      <vue-office-excel
        v-else-if="previewType === 'excel' && documentSrc && isReady"
        :key="`excel-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- PDF 文档预览 -->
      <vue-office-pdf
        v-else-if="previewType === 'pdf' && documentSrc && isReady"
        :key="`pdf-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- PowerPoint 文档预览 -->
      <vue-office-pptx
        v-else-if="previewType === 'pptx' && documentSrc && isReady"
        :key="`pptx-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <el-icon :size="64" color="#909399">
          <Document />
        </el-icon>
        <h3>暂不支持预览此文件类型</h3>
        <p>支持的格式：Word (.docx, .doc)、Excel (.xlsx, .xls)、PDF (.pdf)、PowerPoint (.pptx, .ppt)</p>
        <p>请检查文件格式是否正确或联系管理员</p>
        <p class="file-info">当前文件：{{ props.fileName }} ({{ props.fileType }})</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, Warning, Document } from '@element-plus/icons-vue';

// 导入 vue-office 组件
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePdf from '@vue-office/pdf';
import VueOfficePptx from '@vue-office/pptx';
import { fastApiRequest } from '/@/api/iot/knowledgeBase';

// 导入 vue-office 样式文件
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';
// 注意：@vue-office/pdf 和 @vue-office/pptx 不需要CSS文件

// Props 定义
interface Props {
  src: string | ArrayBuffer | Blob;
  fileType?: string;
  fileName?: string;
  height?: string;
  width?: string;
}

const props = withDefaults(defineProps<Props>(), {
  fileType: '',
  fileName: '',
  height: '100%',
  width: '100%'
});

// Emits 定义
const emit = defineEmits<{
  rendered: [];
  error: [error: string];
}>();

// 响应式数据
const loading = ref(false);
const error = ref('');
const documentSrc = ref<string | ArrayBuffer | Blob>('');
const isLoading = ref(false); // 防止重复加载
const isReady = ref(false); // DOM是否准备就绪
const renderKey = ref(0); // 强制重新渲染的key

// 根据官方GitHub示例，Excel组件不需要options配置
// 官方示例非常简单，直接使用src即可

// 获取正确的MIME类型
const getContentType = (previewType: string, fileName?: string): string => {
  const ext = fileName?.toLowerCase().split('.').pop();

  switch (previewType) {
    case 'excel':
      return ext === 'xls' ? 'application/vnd.ms-excel' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'pptx':
      return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'pdf':
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
};

// 计算属性
const previewType = computed(() => {
  const fileType = props.fileType?.toLowerCase() || '';
  const fileName = props.fileName?.toLowerCase() || '';

  // 简化调试信息
  console.log('文件类型检测:', { fileName, fileType });

  // 优先根据文件名判断预览类型（更准确）
  // Excel 文档 - 优先检测
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    console.log('检测为 Excel 文档 (文件名)');
    return 'excel';
  }

  // PowerPoint 文档 - 优先检测
  if (fileName.endsWith('.pptx') || fileName.endsWith('.ppt')) {
    console.log('检测为 PowerPoint 文档 (文件名)');
    return 'pptx';
  }

  // Word 文档 - 优先检测
  if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
    console.log('检测为 Word 文档 (文件名)');
    return 'docx';
  }

  // PDF 文档 - 优先检测
  if (fileName.endsWith('.pdf')) {
    console.log('检测为 PDF 文档 (文件名)');
    return 'pdf';
  }

  // 如果文件名检测失败，回退到fileType检测
  if (fileType.includes('excel') || fileType.includes('xlsx') || fileType.includes('xls')) {
    console.log('检测为 Excel 文档 (类型)');
    return 'excel';
  }

  if (fileType.includes('powerpoint') || fileType.includes('pptx') || fileType.includes('ppt')) {
    console.log('检测为 PowerPoint 文档 (类型)');
    return 'pptx';
  }

  if (fileType.includes('word') || fileType.includes('docx') || fileType.includes('doc')) {
    console.log('检测为 Word 文档 (类型)');
    return 'docx';
  }

  if (fileType.includes('pdf')) {
    console.log('检测为 PDF 文档 (类型)');
    return 'pdf';
  }

  console.log('未识别的文件类型');
  return 'unsupported';
});

const previewStyle = computed(() => {
  const baseStyle = {
    height: props.height || '600px',
    width: props.width || '100%',
    minHeight: '400px'
  };

  // 为Excel和PowerPoint添加特殊样式
  if (previewType.value === 'excel' || previewType.value === 'pptx') {
    return {
      ...baseStyle,
      height: '600px', // 固定高度
      overflow: 'auto'
    };
  }

  return baseStyle;
});

// 方法
const loadDocument = async () => {
  if (!props.src) {
    error.value = '缺少文档源';
    return;
  }

  // 防止重复加载
  if (isLoading.value) {
    console.log('文档正在加载中，跳过重复请求');
    return;
  }

  isLoading.value = true;
  loading.value = true;
  error.value = '';

  // 强制重新渲染组件
  renderKey.value++;

  try {
    console.log('设置文档源:', props.src);
    console.log('文档类型:', previewType.value);

    // 根据官方示例分析，vue-office组件最适合直接使用URL字符串
    // 但我们的API需要认证，所以继续使用ArrayBuffer方式，但要优化配置

    if (typeof props.src === 'string' && (props.src.startsWith('http') || props.src.startsWith('/'))) {
      console.log('获取文档内容并创建Blob URL');
      console.log('文档类型:', previewType.value);

      // 检查文件格式支持
      if (previewType.value === 'excel' && props.fileName?.toLowerCase().endsWith('.xls')) {
        console.warn('警告：vue-office目前不支持.xls文件，只支持.xlsx文件');
        throw new Error('不支持.xls文件预览，请使用.xlsx格式');
      }

      // 获取文档内容
      const response = await fastApiRequest.get(props.src, {
        responseType: 'arraybuffer'
      });

      console.log('文档获取成功，大小:', response.data.byteLength);

      // 创建Blob URL，让vue-office像访问静态文件一样访问
      const blob = new Blob([response.data], {
        type: getContentType(previewType.value, props.fileName)
      });

      // 清理之前的Blob URL
      if (documentSrc.value && typeof documentSrc.value === 'string' && documentSrc.value.startsWith('blob:')) {
        URL.revokeObjectURL(documentSrc.value);
      }

      // 创建新的Blob URL
      const blobUrl = URL.createObjectURL(blob);
      console.log('创建Blob URL成功:', blobUrl);

      documentSrc.value = blobUrl;
    } else {
      // 直接使用传入的数据
      console.log('直接使用传入的数据:', typeof props.src);
      documentSrc.value = props.src;
    }
  } catch (err) {
    error.value = `加载文档失败: ${err instanceof Error ? err.message : '未知错误'}`;
    emit('error', error.value);
  } finally {
    loading.value = false;
    isLoading.value = false; // 重置加载状态，允许下次加载
  }
};

// 事件处理
const handleRendered = () => {
  loading.value = false;
  console.log('Vue Office 文档渲染完成');
  console.log('文档类型:', previewType.value);
  emit('rendered');
};

const handleError = (err: any) => {
  loading.value = false;

  // 按照官方示例的简单错误处理
  console.error('Vue Office 渲染失败:', err);
  console.error('文档类型:', previewType.value);
  console.error('文件名:', props.fileName);

  let errorMsg = `文档渲染失败: ${err?.message || '未知错误'}`;

  // 特殊错误提示
  if (previewType.value === 'excel' && props.fileName?.toLowerCase().endsWith('.xls')) {
    errorMsg = '不支持.xls文件预览，vue-office目前只支持.xlsx格式';
  }

  error.value = errorMsg;
  ElMessage.error(errorMsg);
  emit('error', errorMsg);
};

// 生命周期
onMounted(async () => {
  // 等待DOM渲染完成
  await nextTick();
  isReady.value = true;
  console.log('VueOfficePreview DOM准备就绪');
});

// 组件卸载时清理Blob URL
onUnmounted(() => {
  if (documentSrc.value && typeof documentSrc.value === 'string' && documentSrc.value.startsWith('blob:')) {
    console.log('清理Blob URL:', documentSrc.value);
    URL.revokeObjectURL(documentSrc.value);
  }
});

// 优化：统一监听关键props变化，避免重复请求
watch(() => ({
  src: props.src,
  fileName: props.fileName,
  fileType: props.fileType
}), (newProps, oldProps) => {
  // 只有当关键属性真正发生变化且src不为空时才重新加载
  if (newProps.src && (
    newProps.src !== oldProps?.src ||
    newProps.fileName !== oldProps?.fileName ||
    newProps.fileType !== oldProps?.fileType
  )) {
    console.log('文档属性变化，重新加载:', newProps);
    loadDocument();
  }
}, { immediate: true, deep: true });
</script>

<style scoped>
.vue-office-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-loading,
.preview-error,
.unsupported-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #909399;
  padding: 40px;
  text-align: center;
}

.office-preview-container {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.unsupported-preview h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.unsupported-preview p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.unsupported-preview .file-info {
  margin-top: 16px;
  font-size: 12px;
  color: #c0c4cc;
  font-family: monospace;
}
</style>
